<script setup lang="ts">
import { ref, onMounted } from "vue";
import TheSchoolBagFormYearly from "~~/components/form/school-bag/TheSchoolBagFormYearly.vue";
import TheSchoolBagFormLogined from "~~/components/form/school-bag/TheSchoolBagFormLogined.vue";
import { useRoute, useRuntimeConfig, navigateTo } from "#app";
import { useAuthStore } from "~~/src/stores/auth";
import Products from "~~/src/models/Products";
import Orders from "~~/src/models/Orders";
import type RandselOrder from "~~/src/models/entry/RandselOrder";

import { useHead } from "unhead";

// ルートとクエリパラメータの取得
const route = useRoute();
const config = useRuntimeConfig();
const authStore = useAuthStore();

// リアクティブデータ
const products = ref<TProduct[]>([]);
const filteredProducts = ref<TProduct[]>([]);
const selectedGender = ref<number>(0);
const isLoading = ref(true);
const isLoadingProducts = ref(false);
const stepChanged = ref(false);

const isLogined = !!authStore.getAccessToken();
const orders = ref<RandselOrder[]>([]);

// SEO設定
if (config.public.isProduction) {
    useHead({
        meta: [
            {
                name: "google-site-verification",
                content: "sd6BXRMajQVpR39NUe8I7YxWQyHFFqf0Qvpfx_0xKIQ",
            },
        ],
    });
}

// ページタイトル設定
useHead({
    title: "2027年度ランドセルカタログ一覧 - カバーミー",
    meta: [
        {
            name: "description",
            content: "2027年度（現在年中さん向け）ランドセルカタログの一覧ページです。性別に応じたおすすめカタログを一括請求できます。",
        },
    ],
});

// 性別に応じて商品をフィルタリングする関数（index.vueと同じロジック）
const filterProductsByGender = (gender: number) => {
    if (gender === 0) {
        filteredProducts.value = products.value;
    } else if (gender === 1) {
        // 男の子向け：性別指定なしメーカー（女の子限定メーカーを除く）
        filteredProducts.value = products.value.filter(product => {
            const brand = product.brand as { id?: number; name?: string; target_gender?: number; } | null;
            const brandTargetGender = brand?.target_gender || 3;
            return brandTargetGender !== 2;
        });
    } else if (gender === 2) {
        // 女の子向け：性別指定なしメーカー（男の子限定メーカーを除く）
        filteredProducts.value = products.value.filter(product => {
            const brand = product.brand as { id?: number; name?: string; target_gender?: number; } | null;
            const brandTargetGender = brand?.target_gender || 3;
            return brandTargetGender !== 1;
        });
    }
};

// ステップ変更ハンドラー
const handleStepChanged = (isStepChanged: boolean) => {
    stepChanged.value = isStepChanged;
};

// 初期化処理
onMounted(async () => {
    try {
        // クエリパラメータの検証
        const genderParam = route.query.recommend;

        // パラメータの妥当性チェック
        if (!genderParam) {
            console.error("必要なパラメータが不足しています");
            await navigateTo("/form/randsel/2027/");
            return;
        }

        const gender = parseInt(genderParam as string);

        // 性別パラメータの妥当性チェック（0, 1, 2のみ許可）
        if (![0, 1, 2].includes(gender)) {
            console.error("不正な性別パラメータです");
            await navigateTo("/form/randsel/2027/");
            return;
        }

        selectedGender.value = gender;

        // ログインユーザーの場合のみ注文データを取得
        if (isLogined) {
            try {
                orders.value = await Orders.create(config).index();
            } catch (error) {
                console.error("Error fetching orders:", error);
                authStore.clearAuth();
                await navigateTo("/member/account/");
                return;
            }
        }

        // 商品データの取得
        isLoadingProducts.value = true;
        try {
            const productList = await Products.create(config).getList({ year: 2027 });
            if (productList) {
                products.value = productList.map((product) => product.data);
                // 性別に応じてフィルタリング
                filterProductsByGender(gender);
            }
        } catch (error) {
            console.error("Error fetching products:", error);
            // エラー時はindex.vueにリダイレクト
            await navigateTo("/form/randsel/2027/");
            return;
        } finally {
            isLoadingProducts.value = false;
        }

    } catch (error) {
        console.error("初期化エラー:", error);
        await navigateTo("/form/randsel/2027/");
        return;
    } finally {
        isLoading.value = false;
    }
});
</script>

<template>
    <div>
        <!-- ローディング表示 -->
        <div v-if="isLoading || isLoadingProducts" class="coverme-progress-circular">
            <v-progress-circular indeterminate :size="50" />
            <p class="mt-2">カタログ情報を読み込み中...</p>
        </div>

        <!-- カタログフォーム表示 -->
        <div v-else-if="!isLoading && !stepChanged">
            <div v-if="isLogined">
                <the-school-bag-form-logined
                    v-if="filteredProducts.length > 0 && orders.length > 0"
                    :products-data="filteredProducts"
                    :order-data="orders"
                />
            </div>
            <div v-else>
                <the-school-bag-form-yearly
                    v-if="filteredProducts.length"
                    :products-data="filteredProducts"
                    :selected-gender="selectedGender"
                    @step-changed="handleStepChanged"
                />
            </div>
        </div>

        <!-- ステップ変更時の表示 -->
        <div v-if="stepChanged" class="coverme-progress-circular">
            <v-progress-circular indeterminate :size="50" />
        </div>
    </div>
</template>

<style scoped>
.coverme-progress-circular {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}
</style>
