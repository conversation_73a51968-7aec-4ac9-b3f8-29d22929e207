<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRuntimeConfig, navigateTo } from "#app";
import { useHead } from "unhead";

const isLoading = ref(true);

// authStoreはlist.vueページで使用するため、ここでは不要
const config = useRuntimeConfig();

// 段階的表示制御用のリアクティブデータ
const currentStep = ref<'initial' | 'gender'>('initial');
const selectedGender = ref(1);


// ログイン状態とorders関連はlist.vueページで使用するため、ここでは不要
// const isLogined = !!authStore.getAccessToken();
// const member = ref<Member>();
// const orders = ref<RandselOrder[]>([]);

if (config.public.isProduction) {
    useHead({
        meta: [
            {
                name: "google-site-verification",
                content: "sd6BXRMajQVpR39NUe8I7YxWQyHFFqf0Qvpfx_0xKIQ",
            },
        ],
    });
}

// 第1段階から第2段階への遷移（商品データ取得開始）
const toGenderSelection = async () => {
    currentStep.value = 'gender';

    // 商品データがまだ取得されていない場合のみ取得
    // if (products.value.length === 0) {
    //     isLoadingProducts.value = true;
    //     try {
    //         const productList = await Products.create(config).getList({ year: 2027 });
    //         if (productList) {
    //             products.value = productList.map((product) => product.data);
    //         }
    //     } catch (error) {
    //         console.error("Error fetching products:", error);
    //     } finally {
    //         isLoadingProducts.value = false;
    //     }
    // }
};

// 性別選択とlist.vueページへの遷移
const selectGender = async (gender: number) => {
    selectedGender.value = gender;

    // list.vueページに遷移（クエリパラメータで状態を渡す）
    await navigateTo(`/form/randsel/2027/list?recommend=${gender}`);
};

// 初期化処理を行う
onMounted(async () => {
    // index.vueページでは初期表示のみ行い、データ取得はlist.vueページで実行
    isLoading.value = false;
});
</script>

<template>
    <div>
        <!-- 第1段階：初期表示 -->
        <v-row justify="center">
            <v-col cols="12" md="12" sm="12" class="px-0 text-center font-weight-bold text-primary text-h6">
                <div>無料のカタログ請求でまるっと比較！</div>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pa-0 text-center text-body-2">
                <div>2027年度（現在年中さん向け）請求フォーム</div>
            </v-col>
        </v-row>
        <v-row v-if="currentStep === 'initial'" justify="center">
            <v-col cols="12" md="4" sm="12">
                <v-btn
                    block
                    size="large"
                    variant="elevated"
                    elevation="4"
                    @click="toGenderSelection"
                >
                    <!-- テキストをspanで囲む -->
                    <span class="button-label">次に進む</span>
    
                    <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                    <v-icon
                    class="button-icon text-primary"
                    icon="mdi-chevron-right"
                    aria-hidden="true"
                    ></v-icon>
                </v-btn>
            </v-col>
        </v-row>
    
        <!-- 第2段階：性別選択 -->
        <template v-if="currentStep === 'gender'">
            <!-- 性別選択ボタン -->
            <v-row justify="center">
                <v-col cols="12" md="4" sm="12">
                    <v-btn
                        block
                        variant="elevated"
                        elevation="4"
                        size="large"
                        @click="selectGender(1)"
                    >
                        <!-- テキストをspanで囲む -->
                        <span class="button-label">男の子にオススメ</span>
    
                        <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                        <v-icon
                        class="button-icon text-primary"
                        icon="mdi-chevron-right"
                        aria-hidden="true"
                        ></v-icon>
                    </v-btn>
                </v-col>
            </v-row>
            <v-row justify="center">
                <v-col cols="12" md="4" sm="12">
                    <v-btn
                        block
                        variant="elevated"
                        elevation="4"
                        size="large"
                        @click="selectGender(2)"
                    >
                        <!-- テキストをspanで囲む -->
                        <span class="button-label">女の子にオススメ</span>
    
                        <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                        <v-icon
                        class="button-icon text-primary"
                        icon="mdi-chevron-right"
                        aria-hidden="true"
                        ></v-icon>
                    </v-btn>
                </v-col>
            </v-row>
            <v-row justify="center">
                <v-col cols="12" md="4" sm="12">
                    <v-btn
                        block
                        variant="elevated"
                        elevation="4"
                        size="large"
                        @click="selectGender(0)"
                    >
                        <!-- テキストをspanで囲む -->
                        <span class="button-label">みんなにオススメ</span>
    
                        <!-- アイコンをスロット内に配置し、CSSで位置を制御 -->
                        <v-icon
                        class="button-icon text-primary"
                        icon="mdi-chevron-right"
                        aria-hidden="true"
                        ></v-icon>
                    </v-btn>
                </v-col>
            </v-row>
        </template>
        <v-img class="my-4" src="~/assets/image_2025_9_4.png"></v-img>
    
        <!-- 第3段階は削除：性別選択後にlist.vueページに遷移するため不要 -->
    
        <!-- ローディング表示 -->
        <div v-if="isLoading" class="coverme-progress-circular">
            <v-progress-circular indeterminate :size="50" />
        </div>
    </div>
</template>

<style scoped>
.coverme-progress-circular {
    display: flex;
    justify-content: center;
    align-items: center;
}

.button-label {
  /* ラベルが常に中央に来るように調整 */
  width: 100%;
  text-align: center;
}

.button-icon {
  /* アイコンをボタンの右端に絶対配置 */
  position: absolute;
  right: 16px; /* 右からの距離 */
  top: 50%; /* 上下中央に配置するための準備 */
  transform: translateY(-50%); /* 上下中央に配置 */

  /* アイコンをクリックしてもボタンのイベントが発火するように */
  pointer-events: none;
}
</style>
