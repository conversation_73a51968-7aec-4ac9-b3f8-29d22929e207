<?php

declare(strict_types=1);

namespace App\Command;

use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;
use App\Utility\Encryptor;

use Cake\Command\Command;
use Cake\Console\Arguments;
use Cake\Console\ConsoleIo;
use Cake\Console\ConsoleOptionParser;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Exception;

/**
 * 一般ユーザーのデータを抽出するCakePHPコンソールコマンド
 *
 * 目的：
 * - GENERAL_USERSとUSER_PROFILESテーブルからデータを抽出
 * - 退会済みユーザーを除外（GENERAL_USERS.deleted IS NULL）
 * - 暗号化されたフィールドを復号化
 * - CSV形式でShift-JISエンコードで出力
 *
 * 実行方法：
 * docker-compose exec amazonlinux2 php bin/cake extract_general_users
 */
class ExtractGeneralUsersCommand extends Command
{
    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;

    private int $totalCount = 0;
    private int $processedCount = 0;
    private int $errorCount = 0;
    private array $errors = [];

    /**
     * 都道府県リスト（コード => 名前）
     */
    private const PREFECTURE_LIST = [
        '01' => '北海道',
        '02' => '青森県',
        '03' => '岩手県',
        '04' => '宮城県',
        '05' => '秋田県',
        '06' => '山形県',
        '07' => '福島県',
        '08' => '茨城県',
        '09' => '栃木県',
        '10' => '群馬県',
        '11' => '埼玉県',
        '12' => '千葉県',
        '13' => '東京都',
        '14' => '神奈川県',
        '15' => '新潟県',
        '16' => '富山県',
        '17' => '石川県',
        '18' => '福井県',
        '19' => '山梨県',
        '20' => '長野県',
        '21' => '岐阜県',
        '22' => '静岡県',
        '23' => '愛知県',
        '24' => '三重県',
        '25' => '滋賀県',
        '26' => '京都府',
        '27' => '大阪府',
        '28' => '兵庫県',
        '29' => '奈良県',
        '30' => '和歌山県',
        '31' => '鳥取県',
        '32' => '島根県',
        '33' => '岡山県',
        '34' => '広島県',
        '35' => '山口県',
        '36' => '徳島県',
        '37' => '香川県',
        '38' => '愛媛県',
        '39' => '高知県',
        '40' => '福岡県',
        '41' => '佐賀県',
        '42' => '長崎県',
        '43' => '熊本県',
        '44' => '大分県',
        '45' => '宮崎県',
        '46' => '鹿児島県',
        '47' => '沖縄県',
    ];

    /**
     * コマンドオプションの定義
     */
    public function buildOptionParser(ConsoleOptionParser $parser): ConsoleOptionParser
    {
        $parser
            ->setDescription([
                '一般ユーザーのデータを抽出するCakePHPコンソールコマンド',
                '',
                'GENERAL_USERSとUSER_PROFILESテーブルからデータを抽出し、',
                'CSV形式でShift-JISエンコードで出力します。',
                '',
                '抽出条件：',
                '- 退会済みユーザーを除外（GENERAL_USERS.deleted IS NULL）',
                '- 暗号化されたフィールドを復号化',
                '',
                '出力項目：',
                '- E-Mail, 都道府県, カバーミーメルマガ拒否フラグ, カバーミー登録日',
            ])
            ->addOption('batch-size', [
                'help' => 'バッチサイズ（一度に処理する件数）',
                'short' => 'b',
                'default' => 1000,
            ])
            ->addOption('verbose', [
                'help' => '詳細ログを出力',
                'short' => 'v',
                'boolean' => true,
                'default' => false,
            ]);

        return $parser;
    }

    /**
     * コマンド実行
     */
    public function execute(Arguments $args, ConsoleIo $io): ?int
    {
        $batchSize = (int)$args->getOption('batch-size');
        $verbose = (bool)$args->getOption('verbose');

        $io->out('一般ユーザーデータ抽出処理を開始します。');
        $io->out("バッチサイズ: {$batchSize}");

        if ($batchSize < 1 ) {
            $io->error('バッチサイズは1以上を指定してください。');
            return static::CODE_ERROR;
        }

        try {
            $this->initializeCommand();

            // 出力ファイル名を生成
            $timestamp = FrozenTime::now()->format('Ymd_His');
            $outputFile = TMP . "general_users_{$timestamp}.csv";

            // データ抽出とCSV出力
            $this->extractAndOutputCsv($outputFile, $batchSize, $io, $verbose);

            // 結果表示
            $this->displayResults($io, $outputFile);

            return static::CODE_SUCCESS;

        } catch (Exception $e) {
            $io->error("抽出処理中にエラーが発生しました: " . $e->getMessage());
            Log::error("ExtractGeneralUsersCommand failed: " . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return static::CODE_ERROR;
        }
    }

    /**
     * 初期化処理
     */
    private function initializeCommand(): void
    {
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');

        $this->totalCount = 0;
        $this->processedCount = 0;
        $this->errorCount = 0;
        $this->errors = [];
    }

    /**
     * データ抽出とCSV出力
     */
    private function extractAndOutputCsv(string $outputFile, int $batchSize, ConsoleIo $io, bool $verbose): void
    {
        // 総件数を取得
        $this->totalCount = $this->generalUsersTable->find()
            ->where(['GeneralUsers.deleted IS' => null])
            ->count();

        if ($verbose) {
            $io->out("対象ユーザー数: {$this->totalCount}件");
        }

        // CSVファイルを開く
        $fp = fopen($outputFile, 'w');
        if (!$fp) {
            throw new Exception("CSVファイルを作成できませんでした: {$outputFile}");
        }

        // BOMを追加（Shift-JIS用）
        // fwrite($fp, "\xEF\xBB\xBF");

        // CSVヘッダを書き込み
        $headers = ['E-Mail', '都道府県', 'カバーミーメルマガ拒否フラグ', 'カバーミー登録日'];
        $this->writeCsvLine($fp, $headers);

        // バッチ処理でデータを取得・出力
        $offset = 0;
        while ($offset < $this->totalCount) {
            if ($verbose) {
                $io->out("処理中: {$offset} - " . min($offset + $batchSize, $this->totalCount) . " / {$this->totalCount}");
            }

            $users = $this->generalUsersTable->find()
                ->contain(['UserProfiles'])
                ->where(['GeneralUsers.deleted IS' => null])
                ->limit($batchSize)
                ->offset($offset)
                ->toArray();

            foreach ($users as $user) {
                $this->processUserRecord($user, $fp, $verbose);
            }

            $offset += $batchSize;
        }

        fclose($fp);

        // Shift-JISに変換
        $this->convertToShiftJis($outputFile);
    }

    /**
     * ユーザーレコードの処理
     */
    private function processUserRecord($user, $fp, bool $verbose): void
    {
        try {
            $email = $user->email ?? '';
            $prefecture = '';
            $emailSendNgFlg = '';
            $created = '';

            // UserProfileが存在する場合
            if ($user->user_profile) {
                // 都道府県コードを復号化して都道府県名に変換
                $prefectureCode = $user->user_profile->decrypted_prefecture_code;
                if ($prefectureCode && isset(self::PREFECTURE_LIST[$prefectureCode])) {
                    $prefecture = self::PREFECTURE_LIST[$prefectureCode];
                }

                // メール送信拒否フラグ（明示的に0または1を出力）
                $emailSendNgFlg = $user->user_profile->email_send_ng_flg ? '1' : '0';
            } else {
                // UserProfileが存在しない場合はデフォルト値を設定
                $emailSendNgFlg = '1';
            }

            // 登録日をフォーマット
            if ($user->created) {
                $created = $user->created->format('Y/n/j');
            }

            // CSVデータを書き込み
            $csvData = [$email, $prefecture, $emailSendNgFlg, $created];
            $this->writeCsvLine($fp, $csvData);

            $this->processedCount++;

        } catch (Exception $e) {
            $this->errorCount++;
            $error = "ユーザーID {$user->id} の処理中にエラー: " . $e->getMessage();
            $this->errors[] = $error;
            
            if ($verbose) {
                Log::warning($error);
            }
        }
    }

    /**
     * CSV行を書き込み
     */
    private function writeCsvLine($fp, array $data): void
    {
        fputcsv($fp, $data, ',', '"');
    }

    /**
     * ファイルをShift-JISに変換
     */
    private function convertToShiftJis(string $filePath): void
    {
        $content = file_get_contents($filePath);
        $shiftJisContent = mb_convert_encoding($content, 'SJIS-win', 'UTF-8');
        file_put_contents($filePath, $shiftJisContent);
    }

    /**
     * 結果表示
     */
    private function displayResults(ConsoleIo $io, string $outputFile): void
    {
        $io->out('');
        $io->out('=== 抽出結果 ===');
        $io->out("対象ユーザー数: {$this->totalCount}件");
        $io->out("処理完了: {$this->processedCount}件");
        $io->out("エラー: {$this->errorCount}件");
        $io->out("出力ファイル: {$outputFile}");

        if (!empty($this->errors)) {
            $io->out('');
            $io->out('=== エラー詳細 ===');
            foreach ($this->errors as $error) {
                $io->error($error);
            }
        }

        $io->out('');
        $io->out('一般ユーザーデータ抽出処理が完了しました。');
    }
}
