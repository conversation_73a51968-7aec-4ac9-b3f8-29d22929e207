<?php

declare(strict_types=1);

namespace App\Test\TestCase\Command;

use App\Command\ExtractGeneralUsersCommand;
use App\Model\Table\GeneralUsersTable;
use App\Model\Table\UserProfilesTable;

use Cake\Console\TestSuite\ConsoleIntegrationTestTrait;
use Cake\I18n\FrozenTime;
use Cake\Log\Log;
use Cake\ORM\TableRegistry;
use Cake\TestSuite\TestCase;

/**
 * ExtractGeneralUsersCommand Test Case
 *
 * @uses \App\Command\ExtractGeneralUsersCommand
 */
class ExtractGeneralUsersCommandTest extends TestCase
{
    use ConsoleIntegrationTestTrait;

    /**
     * Test fixtures
     */
    protected $fixtures = [
        'app.GeneralUsers',
        'app.UserProfiles',
    ];

    private GeneralUsersTable $generalUsersTable;
    private UserProfilesTable $userProfilesTable;

    /**
     * setUp method
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->useCommandRunner();
        
        $this->generalUsersTable = TableRegistry::getTableLocator()->get('GeneralUsers');
        $this->userProfilesTable = TableRegistry::getTableLocator()->get('UserProfiles');
    }

    /**
     * tearDown method
     */
    public function tearDown(): void
    {
        parent::tearDown();
        Log::drop('debug');
        Log::drop('error');
        
        // テスト用CSVファイルを削除
        $this->cleanupTestFiles();
    }

    /**
     * コマンドオプションのテスト
     */
    public function testBuildOptionParser(): void
    {
        $command = new ExtractGeneralUsersCommand();
        $parser = $command->buildOptionParser($command->getOptionParser());

        // オプションの存在確認
        $options = $parser->options();
        $this->assertArrayHasKey('batch-size', $options);
        $this->assertArrayHasKey('verbose', $options);
        
        // デフォルト値の確認
        $this->assertEquals(1000, $options['batch-size']->defaultValue());
        $this->assertFalse($options['verbose']->defaultValue());
    }

    /**
     * 正常な抽出処理のテスト
     */
    public function testExecuteNormalExtraction(): void
    {
        // テストデータを準備
        $this->createTestData();

        // コマンド実行
        $this->exec('extract_general_users --batch-size=10 --verbose');

        // 結果検証
        $this->assertExitSuccess();
        $this->assertOutputContains('一般ユーザーデータ抽出処理を開始します');
        $this->assertOutputContains('バッチサイズ: 10');
        $this->assertOutputContains('対象ユーザー数:');
        $this->assertOutputContains('抽出結果');
        $this->assertOutputContains('一般ユーザーデータ抽出処理が完了しました');
        $this->assertOutputContains('出力ファイル:');
    }

    /**
     * バッチサイズ指定のテスト
     */
    public function testExecuteWithBatchSize(): void
    {
        $this->exec('extract_general_users --batch-size=5');

        $this->assertExitSuccess();
        $this->assertOutputContains('バッチサイズ: 5');
    }

    /**
     * 詳細ログ出力のテスト
     */
    public function testExecuteVerboseMode(): void
    {
        // テストデータを準備
        $this->createTestData();

        $this->exec('extract_general_users --verbose');

        $this->assertExitSuccess();
        $this->assertOutputContains('対象ユーザー数:');
        $this->assertOutputContains('処理中:');
    }

    /**
     * CSVファイル出力のテスト
     */
    public function testCsvFileOutput(): void
    {
        // テストデータを準備
        $this->createTestData();

        $this->exec('extract_general_users --batch-size=10');

        $this->assertExitSuccess();

        // 出力ファイルの存在確認
        $files = glob(TMP . 'general_users_*.csv');
        $this->assertNotEmpty($files, 'CSVファイルが作成されていません');

        $csvFile = $files[0];
        $this->assertFileExists($csvFile);

        // CSVファイルの内容確認
        $content = file_get_contents($csvFile);
        $this->assertNotEmpty($content);

        // Shift-JISからUTF-8に変換してヘッダーを確認
        $utf8Content = mb_convert_encoding($content, 'UTF-8', 'SJIS-win');
        $lines = explode("\n", $utf8Content);
        $this->assertStringContainsString('E-Mail', $lines[0]);
        $this->assertStringContainsString('都道府県', $lines[0]);
        $this->assertStringContainsString('カバーミーメルマガ拒否フラグ', $lines[0]);
        $this->assertStringContainsString('カバーミー登録日', $lines[0]);
    }

    /**
     * データ変換のテスト
     */
    public function testDataConversion(): void
    {
        // 特定のテストデータを作成
        $user = $this->generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null,
            'created' => FrozenTime::create(2024, 1, 15, 10, 30, 0),
        ]);
        $savedUser = $this->generalUsersTable->save($user);

        $profile = $this->userProfilesTable->newEntity([
            'general_user_id' => $savedUser->id,
            'last_name' => 'テスト',
            'first_name' => '太郎',
            'last_name_kana' => 'テスト',
            'first_name_kana' => 'タロウ',
            'zip_code' => '100-0001',
            'prefecture_code' => '13', // 東京都
            'address1' => '千代田区千代田',
            'address2' => '1-1-1',
            'tel' => '03-1234-5678',
            'email_send_ng_flg' => true,
        ]);
        $this->userProfilesTable->save($profile);

        $this->exec('extract_general_users');

        $this->assertExitSuccess();

        // CSVファイルの内容確認
        $files = glob(TMP . 'general_users_*.csv');
        $csvFile = $files[0];
        $content = file_get_contents($csvFile);

        // Shift-JISからUTF-8に変換してデータ変換を確認
        $utf8Content = mb_convert_encoding($content, 'UTF-8', 'SJIS-win');
        $this->assertStringContainsString('<EMAIL>', $utf8Content);
        $this->assertStringContainsString('東京都', $utf8Content); // 都道府県コード変換
        $this->assertStringContainsString('1', $utf8Content); // email_send_ng_flg
        $this->assertStringContainsString('2024/1/15', $utf8Content); // 日付フォーマット
    }

    /**
     * 退会済みユーザー除外のテスト
     */
    public function testExcludeDeletedUsers(): void
    {
        // 通常ユーザー
        $activeUser = $this->generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null,
            'deleted' => null,
            'created' => FrozenTime::now(),
        ]);
        $this->generalUsersTable->save($activeUser);

        // 退会済みユーザー
        $deletedUser = $this->generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null,
            'deleted' => FrozenTime::now(),
            'created' => FrozenTime::now(),
        ]);
        $this->generalUsersTable->save($deletedUser);

        $this->exec('extract_general_users');

        $this->assertExitSuccess();

        // CSVファイルの内容確認
        $files = glob(TMP . 'general_users_*.csv');
        $csvFile = $files[0];
        $content = file_get_contents($csvFile);

        // Shift-JISからUTF-8に変換して確認
        $utf8Content = mb_convert_encoding($content, 'UTF-8', 'SJIS-win');
        // 通常ユーザーは含まれる
        $this->assertStringContainsString('<EMAIL>', $utf8Content);
        // 退会済みユーザーは含まれない
        $this->assertStringNotContainsString('<EMAIL>', $utf8Content);
    }

    /**
     * メール送信拒否フラグが0の場合のテスト
     */
    public function testEmailSendNgFlgZero(): void
    {
        // 既存のテストファイルをクリーンアップ
        $this->cleanupTestFiles();

        // email_send_ng_flgが0（false）のテストデータを作成
        $user = $this->generalUsersTable->newEntity([
            'email' => '<EMAIL>',
            'password' => null,
            'created' => FrozenTime::create(2024, 2, 1, 10, 30, 0),
        ]);
        $savedUser = $this->generalUsersTable->save($user);

        $profile = $this->userProfilesTable->newEntity([
            'general_user_id' => $savedUser->id,
            'last_name' => 'テスト',
            'first_name' => 'ゼロ',
            'last_name_kana' => 'テスト',
            'first_name_kana' => 'ゼロ',
            'zip_code' => '100-0001',
            'prefecture_code' => '27', // 大阪府
            'address1' => '大阪市中央区',
            'address2' => '1-1-1',
            'tel' => '06-1234-5678',
            'email_send_ng_flg' => false, // 明示的にfalse（0）を設定
        ]);
        $this->userProfilesTable->save($profile);

        $this->exec('extract_general_users');

        $this->assertExitSuccess();

        // CSVファイルの内容確認
        $files = glob(TMP . 'general_users_*.csv');
        $this->assertNotEmpty($files, 'CSVファイルが作成されていません');
        $csvFile = $files[0];
        $content = file_get_contents($csvFile);

        // Shift-JISからUTF-8に変換してデータ変換を確認
        $utf8Content = mb_convert_encoding($content, 'UTF-8', 'SJIS-win');
        $this->assertStringContainsString('<EMAIL>', $utf8Content);
        $this->assertStringContainsString('大阪府', $utf8Content);
        $this->assertStringContainsString(',0,', $utf8Content); // email_send_ng_flgが0として出力されることを確認
        $this->assertStringContainsString('2024/2/1', $utf8Content);
    }

    /**
     * エラーハンドリングのテスト
     */
    public function testErrorHandling(): void
    {
        // 無効なバッチサイズでテスト
        $this->exec('extract_general_users --batch-size=0');

        // コマンド自体は成功するが、適切に処理される
        $this->assertExitError();
    }

    /**
     * 都道府県コード変換のテスト
     */
    public function testPrefectureCodeConversion(): void
    {
        $testCases = [
            ['01', '北海道'],
            ['13', '東京都'],
            ['27', '大阪府'],
            ['47', '沖縄県'],
            ['99', ''], // 存在しないコード
        ];

        foreach ($testCases as [$code, $expected]) {
            $user = $this->generalUsersTable->newEntity([
                'email' => "test{$code}@example.com",
                'password' => null,
            ]);
            $savedUser = $this->generalUsersTable->save($user);

            $profile = $this->userProfilesTable->newEntity([
                'general_user_id' => $savedUser->id,
                'last_name' => 'テスト',
                'first_name' => '太郎',
                'last_name_kana' => 'テスト',
                'first_name_kana' => 'タロウ',
                'zip_code' => '100-0001',
                'prefecture_code' => $code,
                'address1' => 'テスト住所',
                'address2' => '1-1-1',
                'tel' => '03-1234-5678',
                'email_send_ng_flg' => false,
            ]);
            $this->userProfilesTable->save($profile);
        }

        $this->exec('extract_general_users');

        $this->assertExitSuccess();

        // CSVファイルの内容確認
        $files = glob(TMP . 'general_users_*.csv');
        $csvFile = $files[0];
        $content = file_get_contents($csvFile);

        // Shift-JISからUTF-8に変換して確認
        $utf8Content = mb_convert_encoding($content, 'UTF-8', 'SJIS-win');
        foreach ($testCases as [$code, $expected]) {
            if ($expected) {
                $this->assertStringContainsString($expected, $utf8Content);
            }
        }
    }

    /**
     * テストデータの作成
     */
    private function createTestData(): void
    {
        // 複数のテストユーザーを作成
        for ($i = 1; $i <= 5; $i++) {
            $user = $this->generalUsersTable->newEntity([
                'email' => "user{$i}@example.com",
                'password' => null,
                'created' => FrozenTime::now()->subDays($i),
            ]);
            $savedUser = $this->generalUsersTable->save($user);

            $profile = $this->userProfilesTable->newEntity([
                'general_user_id' => $savedUser->id,
                'last_name' => "姓{$i}",
                'first_name' => "名{$i}",
                'last_name_kana' => "セイ{$i}",
                'first_name_kana' => "メイ{$i}",
                'zip_code' => '100-000' . $i,
                'prefecture_code' => sprintf('%02d', $i),
                'address1' => "住所{$i}",
                'address2' => '1-1-1',
                'tel' => "03-1234-567{$i}",
                'email_send_ng_flg' => $i % 2 === 0,
            ]);
            $this->userProfilesTable->save($profile);
        }
    }

    /**
     * テストファイルのクリーンアップ
     */
    private function cleanupTestFiles(): void
    {
        $files = glob(TMP . 'general_users_*.csv');
        foreach ($files as $file) {
            if (file_exists($file)) {
                unlink($file);
            }
        }
    }
}
