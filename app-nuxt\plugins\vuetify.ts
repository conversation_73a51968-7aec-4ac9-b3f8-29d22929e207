// import this after install `@mdi/font` package
import "@mdi/font/css/materialdesignicons.css";

import "vuetify/styles";
import { createVuetify } from "vuetify";
import { ja } from "vuetify/locale";

export default defineNuxtPlugin((app) => {
    const vuetify = createVuetify({
        theme: {
            defaultTheme: "myTheme", // デフォルトのテーマを指定
            themes: {
                myTheme: {
                    dark: false,
                    colors: {
                        // primary: "#6200EA", // プライマリーカラー
                        // secondary: "#03DAC6", // セカンダリーカラー
                        // accent: "#82B1FF", // アクセントカラー
                        // error: "#FF5252", // エラーカラー
                        // info: "#2196F3", // 情報カラー
                        // success: "#4CAF50", // 成功カラー
                        // warning: "#FB8C00", // 警告カラー
                        primary: "#e589a2", // --color_main
                        clientPrimary: "#1da800",
                        swbPrimary: "#0092e5",
                        secondary: "#2f4f4f", // --color_text
                        accent: "#39a0ff", // --color_link
                        background: "#f7f0e6", // --color_bg
                        info: "#6599b7", // --color_faq_a
                        success: "#86dd7b", // --color_list_good
                        warning: "#f4e03a", // --color_list_triangle
                        error: "#f36060", // --color_list_bad
                        header: "#ffffff", // --color_header_bg
                        footer: "#e589a2", // --color_footer_bg
                        footerText: "#ffffff", // --color_footer_text
                    },
                    variables: {
                        "theme-on-background": "#2f4f4f",
                        "theme-on-surface": "#2f4f4f",
                        "theme-on-clientPrimary": "#ffffff",
                        "high-emphasis-opacity": 1,
                        "medium-emphasis-opacity": 1,
                    },
                },
            },
        },
        defaults: {
            VContainer: {
                style: "max-width: 1200px;",
            },
        },
        locale: {
            locale: "ja", // デフォルトのロケールを日本語に設定
            messages: { ja }, // 日本語ロケールの翻訳データを指定
        },
    });
    app.vueApp.use(vuetify);
});
